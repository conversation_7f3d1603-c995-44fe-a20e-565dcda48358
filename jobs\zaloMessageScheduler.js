const cron = require('node-cron');
const axios = require('axios');
const Announcement = require('../models/Announcement');
const { ANNOUNCEMENT_STATUS } = require('../constants/announcementConstants');

// Chạy mỗi phút để kiểm tra thông báo cần gửi
const scheduleZaloMessages = () => {
  console.log('🕒 Zalo Message Scheduler started - checking every minute');
  
  cron.schedule('* * * * *', async () => {
    try {
      await processScheduledAnnouncements();
    } catch (error) {
      console.error('❌ Error in Zalo scheduler:', error);
    }
  });
};

// Xử lý các thông báo đã lên lịch
const processScheduledAnnouncements = async () => {
  const now = new Date();
  
  // Tìm các thông báo:
  // 1. Status = 'scheduled' hoặc 'draft' 
  // 2. Có zaloConfig.enabled = true
  // 3. Có scheduledTime <= hiện tại
  // 4. <PERSON><PERSON><PERSON> đ<PERSON> gử<PERSON> (sent = false)
  const scheduledAnnouncements = await Announcement.find({
    status: { $in: [ANNOUNCEMENT_STATUS.SCHEDULED, ANNOUNCEMENT_STATUS.DRAFT] },
    'zaloConfig.enabled': true,
    'zaloConfig.scheduledTime': { $lte: now },
    'zaloConfig.sent': false,
    'zaloConfig.groupId': { $exists: true, $ne: '' }
  }).populate('sender', 'name role');

  if (scheduledAnnouncements.length === 0) {
    return; // Không có thông báo nào cần gửi
  }

  console.log(`📨 Found ${scheduledAnnouncements.length} announcements to send to Zalo`);

  for (const announcement of scheduledAnnouncements) {
    try {
      await sendAnnouncementToZalo(announcement);
    } catch (error) {
      console.error(`❌ Failed to send announcement ${announcement._id} to Zalo:`, error.message);
      
      // Ghi log lỗi vào announcement (có thể thêm field errorLog sau)
      announcement.zaloConfig.lastError = error.message;
      announcement.zaloConfig.lastErrorAt = new Date();
      await announcement.save();
    }
  }
};

// Gửi 1 thông báo cụ thể đến Zalo
const sendAnnouncementToZalo = async (announcement) => {
  const accessToken = process.env.ZALO_ACCESS_TOKEN;
  
  if (!accessToken) {
    throw new Error('Zalo access token không được cấu hình');
  }

  const { groupId, groupName } = announcement.zaloConfig;
  
  // Format message
  const message = formatAnnouncementMessage(announcement);
  
  console.log(`📤 Sending announcement to Zalo group: ${groupName} (${groupId})`);
  
  // Gọi Zalo API
  const response = await axios.post('https://openapi.zalo.me/v3.0/oa/group/message', {
    recipient: {
      group_id: groupId
    },
    message: {
      text: message
    }
  }, {
    headers: {
      'access_token': accessToken,
      'Content-Type': 'application/json'
    }
  });

  if (response.data.error !== 0) {
    throw new Error(`Zalo API Error: ${response.data.message}`);
  }

  // Cập nhật trạng thái đã gửi
  announcement.zaloConfig.sent = true;
  announcement.zaloConfig.sentAt = new Date();
  announcement.status = ANNOUNCEMENT_STATUS.SENT;
  
  await announcement.save();
  
  console.log(`✅ Successfully sent announcement ${announcement._id} to Zalo group ${groupName}`);
};

// Format tin nhắn để gửi Zalo
const formatAnnouncementMessage = (announcement) => {
  const typeLabels = {
    'teacher_to_student': '📚 Thông báo từ giáo viên',
    'principal_to_teacher': '🏫 Thông báo từ hiệu trưởng', 
    'head_to_teacher': '👥 Thông báo từ tổ trưởng',
    'admin_to_all': '📢 Thông báo quan trọng'
  };

  const typeLabel = typeLabels[announcement.type] || '📋 Thông báo';
  const senderName = announcement.sender?.name || 'Hệ thống';
  const timestamp = new Date().toLocaleString('vi-VN');

  let message = `${typeLabel}\n`;
  message += `👤 Từ: ${senderName}\n`;
  message += `⏰ Thời gian: ${timestamp}\n\n`;
  message += `📝 Nội dung:\n${announcement.content}\n\n`;
  message += `---\n`;
  message += `🤖 Tin nhắn tự động từ hệ thống quản lý trường học`;

  return message;
};

// Gửi thông báo Zalo ngay lập tức (không theo schedule)
const sendImmediateZaloMessage = async (announcementId) => {
  try {
    const announcement = await Announcement.findById(announcementId)
      .populate('sender', 'name role');
    
    if (!announcement) {
      throw new Error('Không tìm thấy thông báo');
    }

    if (!announcement.zaloConfig.enabled || !announcement.zaloConfig.groupId) {
      throw new Error('Thông báo chưa được cấu hình Zalo');
    }

    await sendAnnouncementToZalo(announcement);
    return { success: true, message: 'Đã gửi thông báo Zalo thành công' };
    
  } catch (error) {
    console.error('❌ Error sending immediate Zalo message:', error);
    throw error;
  }
};

module.exports = {
  scheduleZaloMessages,
  processScheduledAnnouncements,
  sendImmediateZaloMessage
}; 