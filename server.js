const express = require('express');
const cors = require('cors');
const https = require('https'); // Thêm module https
const fs = require('fs'); // Thêm module fs để đọc file chứng chỉ
const connectDB = require('./config/db');
const config = require('./config/default');
const initExamTimeLimitChecker = require('./jobs/examTimeLimitChecker');
const { scheduleZaloMessages } = require('./jobs/zaloMessageScheduler');

// Kết nối MongoDB
connectDB()
  .then(() => {
    console.log('Đã kết nối tới MongoDB');

    // Khởi tạo cron job kiểm tra thời gian làm bài
    initExamTimeLimitChecker();
    
    // Khởi tạo scheduler gửi tin nhắn Zalo
    scheduleZaloMessages();
  })
  .catch(err => console.error('Lỗi kết nối MongoDB:', err));

const app = express();

// Middleware
app.use(express.json());
app.use(cors());

// Routes
app.use('/api/auth', require('./routes/auth'));
app.use('/api/schedules', require('./routes/schedule'));
app.use('/api/news', require('./routes/news'));
app.use('/api/classes', require('./routes/class'));
app.use('/api/announcements', require('./routes/announcement'));
app.use('/api/events', require('./routes/events'));
app.use('/api/grades', require('./routes/grade'));
app.use('/api/subjects', require('./routes/subject'));
app.use('/api/exams', require('./routes/exam'));
app.use('/api/attendance', require('./routes/attendance'));
app.use('/api/attendance-config', require('./routes/attendanceConfig'));
app.use('/api/leave-requests', require('./routes/leaveRequest'));
app.use('/api/directory', require('./routes/directory'));
app.use('/api/zalo', require('./routes/zalo'));

// Route chính
app.get('/', (req, res) => {
  res.send('API đang chạy');
});

// Cấu hình chứng chỉ SSL
const sslOptions = {
  cert: fs.readFileSync('./certs/certificate.crt'), // Đường dẫn đến file chứng chỉ
  key: fs.readFileSync('./certs/private.key'), // Đường dẫn đến file private key
  ca: fs.readFileSync('./certs/rootca.crt')
};

// Cổng và khởi động server HTTPS
const PORT = config.port; // Giữ nguyên port từ config (ví dụ: 5000)
// https.createServer(sslOptions, app).listen(PORT, () => {
//   console.log(`Server HTTPS đang chạy trên cổng ${PORT}`);
// });

app.listen(PORT, () => {
  console.log(`Server HTTP đang chạy trên cổng ${PORT}`);
});