const asyncHandler = require('express-async-handler');
const axios = require('axios');
const User = require('../models/User');
const MESSAGES = require('../constants/messages');
const { HTTP_STATUS, createSuccessResponse, createErrorResponse } = require('../constants/httpConstants');

// @desc    Lấy danh sách tất cả nhóm Zalo từ API
// @route   GET /api/zalo/groups
// @access  Private (Admin only)
exports.getAllZaloGroups = asyncHandler(async (req, res) => {
  try {
    const accessToken = process.env.ZALO_ACCESS_TOKEN;
    
    if (!accessToken) {
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createErrorResponse('Zalo access token chưa được cấu hình')
      );
    }

    const { count = 20, offset = 0 } = req.query;
    
    const response = await axios.get('https://openapi.zalo.me/v3.0/oa/group/getgroupsofoa', {
      params: {
        count: count,
        offset: offset
      },
      headers: {
        'access_token': accessToken,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.error !== 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Lỗi từ Zalo API', response.data.message)
      );
    }

    res.json(createSuccessResponse(response.data.data));

  } catch (error) {
    console.error('Zalo API Error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse('Lỗi khi gọi Zalo API', error.message)
    );
  }
});

// @desc    Lấy danh sách nhóm Zalo của user hiện tại (để chọn khi tạo thông báo)
// @route   GET /api/zalo/user/groups
// @access  Private
exports.getUserZaloGroups = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id).select('groupZaloIds name');
  
  res.json(createSuccessResponse({
    _id: user._id,
    name: user.name,
    groupZaloIds: user.groupZaloIds || []
  }, 'Danh sách nhóm Zalo của bạn'));
});

// @desc    Gửi tin nhắn đến nhóm Zalo
// @route   POST /api/zalo/send-message
// @access  Private
exports.sendMessageToZaloGroup = asyncHandler(async (req, res) => {
  const { groupId, message } = req.body;

  if (!groupId || !message) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('groupId và message là bắt buộc')
    );
  }

  // Kiểm tra xem user có quyền gửi tin nhắn đến group này không
  const user = await User.findById(req.user._id).select('groupZaloIds');
  const hasAccess = user.groupZaloIds.some(g => g.groupId === groupId);
  
  if (!hasAccess) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse('Bạn không có quyền gửi tin nhắn đến nhóm này')
    );
  }

  try {
    const accessToken = process.env.ZALO_ACCESS_TOKEN;
    
    if (!accessToken) {
      return res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        createErrorResponse('Zalo access token chưa được cấu hình')
      );
    }

    // Call Zalo API để gửi message
    const response = await axios.post('https://openapi.zalo.me/v3.0/oa/message/group', {
      group_id: groupId,
      message: {
        text: message
      }
    }, {
      headers: {
        'access_token': accessToken,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.error !== 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Lỗi khi gửi tin nhắn', response.data.message)
      );
    }

    res.json(createSuccessResponse(response.data, MESSAGES.ZALO.SEND_SUCCESS));

  } catch (error) {
    console.error('Send message error:', error);
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
      createErrorResponse('Lỗi khi gửi tin nhắn', error.message)
    );
  }
});

// @desc    Admin thêm nhóm Zalo cho user bất kỳ
// @route   POST /api/zalo/admin/user/:userId/groups
// @access  Private (Admin only)
exports.adminAddUserToZaloGroup = asyncHandler(async (req, res) => {
  const { userId } = req.params;
  const { groups } = req.body; // Expecting array of {groupId, groupName}

  // Validate input
  if (!groups || !Array.isArray(groups) || groups.length === 0) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('groups phải là một mảng không rỗng')
    );
  }

  // Validate each group object
  for (const group of groups) {
    if (!group.groupId || !group.groupName) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Mỗi group phải có groupId và groupName')
      );
    }
  }

  const user = await User.findById(userId);
  if (!user) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ZALO.USER_NOT_FOUND)
    );
  }

  const addedGroups = [];
  const skippedGroups = [];

  // Process each group
  for (const group of groups) {
    // Kiểm tra xem user đã có group này chưa
    const existingGroup = user.groupZaloIds.find(g => g.groupId === group.groupId);
    
    if (existingGroup) {
      skippedGroups.push({
        groupId: group.groupId,
        groupName: group.groupName,
        reason: 'User đã tham gia nhóm này rồi'
      });
    } else {
      // Thêm group mới
      user.groupZaloIds.push({
        groupId: group.groupId,
        groupName: group.groupName,
        addedAt: new Date()
      });
      addedGroups.push({
        groupId: group.groupId,
        groupName: group.groupName
      });
    }
  }

  await user.save();

  res.json(createSuccessResponse({
    user: {
      _id: user._id,
      name: user.name,
      totalGroups: user.groupZaloIds.length
    },
    addedGroups: addedGroups,
    skippedGroups: skippedGroups,
    summary: {
      totalProcessed: groups.length,
      totalAdded: addedGroups.length,
      totalSkipped: skippedGroups.length
    }
  }, `Đã xử lý nhóm Zalo cho user ${user.name}`));
});

// @desc    Admin xóa nhóm Zalo khỏi user
// @route   DELETE /api/zalo/admin/user/:userId/groups/:groupId
// @access  Private (Admin only)
exports.adminRemoveUserFromZaloGroup = asyncHandler(async (req, res) => {
  const { userId, groupId } = req.params;
  
  const user = await User.findById(userId);
  if (!user) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ZALO.USER_NOT_FOUND)
    );
  }
  
  // Tìm và xóa group
  const groupIndex = user.groupZaloIds.findIndex(g => g.groupId === groupId);
  if (groupIndex === -1) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ZALO.GROUP_NOT_FOUND)
    );
  }

  user.groupZaloIds.splice(groupIndex, 1);
  await user.save();

  res.json(createSuccessResponse({
    _id: user._id,
    name: user.name,
    groupZaloIds: user.groupZaloIds
  }, `Đã xóa nhóm Zalo khỏi user ${user.name} thành công`));
});

// @desc    Admin thêm nhiều user vào 1 nhóm Zalo
// @route   POST /api/zalo/admin/groups/:groupId/users
// @access  Private (Admin only)
exports.adminAddUsersToZaloGroup = asyncHandler(async (req, res) => {
  const { groupId } = req.params;
  const { groupName, userIds } = req.body; // Expecting array of userIds

  // Validate input
  if (!groupName) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('groupName là bắt buộc')
    );
  }

  if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json({
      msg: 'userIds phải là một mảng không rỗng'
    });
  }

  const addedUsers = [];
  const skippedUsers = [];
  const notFoundUsers = [];

  // Process each user
  for (const userId of userIds) {
    try {
      const user = await User.findById(userId);
      
      if (!user) {
        notFoundUsers.push({
          userId: userId,
          reason: 'User không tồn tại'
        });
        continue;
      }

      // Kiểm tra xem user đã có group này chưa
      const existingGroup = user.groupZaloIds.find(g => g.groupId === groupId);
      
      if (existingGroup) {
        skippedUsers.push({
          userId: userId,
          userName: user.name,
          reason: 'User đã tham gia nhóm này rồi'
        });
      } else {
        // Thêm group mới
        user.groupZaloIds.push({
          groupId: groupId,
          groupName: groupName,
          addedAt: new Date()
        });
        
        await user.save();
        
        addedUsers.push({
          userId: userId,
          userName: user.name
        });
      }
    } catch (error) {
      notFoundUsers.push({
        userId: userId,
        reason: 'Lỗi khi xử lý user: ' + error.message
      });
    }
  }

  res.json(createSuccessResponse({
    group: {
      groupId: groupId,
      groupName: groupName
    },
    addedUsers: addedUsers,
    skippedUsers: skippedUsers,
    notFoundUsers: notFoundUsers,
    summary: {
      totalProcessed: userIds.length,
      totalAdded: addedUsers.length,
      totalSkipped: skippedUsers.length,
      totalNotFound: notFoundUsers.length
    }
  }, `Đã xử lý thêm users vào nhóm ${groupName}`));
}); 