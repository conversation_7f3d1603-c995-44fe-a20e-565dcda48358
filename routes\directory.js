// src/routes/directory.js
const express = require('express');
const router = express.Router();
const {
    getUsers,
    getUsersByClass,
    getTeachersByDepartment,
    toggleFavorite,
    getUserDetail,
    getClasses,
    getDepartments,
    createUser,
    updateUser,
    deleteUser,
    createClass,
    updateClass,
    deleteClass,
    addStudentsToClass,
    removeStudentFromClass,
    getTeacherClasses,
    getClassDetails
} = require('../controllers/directoryController');
const { protect, authorize } = require('../middlewares/auth');

// @route   GET /api/directory
// @desc    Lấy danh sách người dùng (có thể lọc theo role)
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
    '/',
    protect,
    getUsers
);

// @route   GET /api/directory/classes
// @desc    Lấy danh sách lớp học
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
    '/classes',
    protect,
    getClasses
);

// @route   GET /api/directory/departments
// @desc    Lấy danh sách bộ môn
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
    '/departments',
    protect,
    getDepartments
);

// @route   GET /api/directory/class/:classId
// @desc    Lấy danh sách người dùng theo lớp
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
    '/class/:classId',
    protect,
    getUsersByClass
);

// @route   GET /api/directory/class/:classId/details
// @desc    Lấy thông tin chi tiết lớp học với đầy đủ giáo viên
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
    '/class/:classId/details',
    protect,
    getClassDetails
);

// @route   GET /api/directory/teacher/classes
// @desc    Lấy các lớp mà giáo viên dạy
// @access  Private (giáo viên)
router.get(
    '/teacher/classes',
    protect,
    authorize('teacher'),
    getTeacherClasses
);

// @route   GET /api/directory/department/:subject
// @desc    Lấy danh sách giáo viên theo bộ môn
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
    '/department/:subject',
    protect,
    getTeachersByDepartment
);

// @route   POST /api/directory/favorite/:userId
// @desc    Thêm/xóa người dùng khỏi danh sách yêu thích
// @access  Private (tất cả người dùng đã đăng nhập)
router.post(
    '/favorite/:userId',
    protect,
    toggleFavorite
);

// @route   GET /api/directory/user/:userId
// @desc    Lấy thông tin chi tiết của một người dùng
// @access  Private (tất cả người dùng đã đăng nhập)
router.get(
    '/user/:userId',
    protect,
    getUserDetail
);

// =========================== ROUTES CHỈ DÀNH CHO ADMIN/TEACHER ===========================

// @route   POST /api/directory/user
// @desc    Tạo người dùng mới (học sinh hoặc giáo viên)
// @access  Private (admin)
router.post(
    '/user',
    protect,
    authorize('admin'),
    createUser
);

// @route   PUT /api/directory/user/:userId
// @desc    Cập nhật thông tin người dùng
// @access  Private (admin, giáo viên chỉ cập nhật được học sinh trong lớp của mình)
router.put(
    '/user/:userId',
    protect,
    authorize('admin', 'teacher'),
    updateUser
);

// @route   DELETE /api/directory/user/:userId
// @desc    Xóa người dùng
// @access  Private (admin)
router.delete(
    '/user/:userId',
    protect,
    authorize('admin'),
    deleteUser
);

// @route   POST /api/directory/class
// @desc    Tạo lớp học mới
// @access  Private (admin)
router.post(
    '/class',
    protect,
    authorize('admin'),
    createClass
);

// @route   PUT /api/directory/class/:classId
// @desc    Cập nhật thông tin lớp học
// @access  Private (admin, giáo viên chủ nhiệm)
router.put(
    '/class/:classId',
    protect,
    authorize('admin', 'teacher'),
    updateClass
);

// @route   DELETE /api/directory/class/:classId
// @desc    Xóa lớp học
// @access  Private (admin)
router.delete(
    '/class/:classId',
    protect,
    authorize('admin'),
    deleteClass
);

// @route   POST /api/directory/class/:classId/students
// @desc    Thêm học sinh vào lớp
// @access  Private (admin, giáo viên chủ nhiệm)
router.post(
    '/class/:classId/students',
    protect,
    authorize('admin', 'teacher'),
    addStudentsToClass
);

// @route   DELETE /api/directory/class/:classId/students/:studentId
// @desc    Xóa học sinh khỏi lớp
// @access  Private (admin, giáo viên chủ nhiệm)
router.delete(
    '/class/:classId/students/:studentId',
    protect,
    authorize('admin', 'teacher'),
    removeStudentFromClass
);

module.exports = router;