/**
 * Constants cho các tổ bộ môn trong trường học
 * Tổ chức theo mô hình các tổ chuyên môn
 */

// Các môn học cơ bản
const SUBJECTS = {
  TOAN: 'To<PERSON>',
  TIN_HOC: '<PERSON> Họ<PERSON>',
  VAN: 'Ngữ Văn',
  LY: 'V<PERSON><PERSON>',
  HOA: '<PERSON><PERSON><PERSON>',
  SINH: '<PERSON><PERSON> Họ<PERSON>',
  SU: 'L<PERSON><PERSON> Sử',
  DIA: '<PERSON><PERSON><PERSON>',
  GDCD: 'GDCD',
  GDTC: 'Thể Dục',
  ANH: 'Tiếng Anh',
  MY_THUAT: '<PERSON><PERSON> Thuậ<PERSON>',
  AM_NHAC: 'Âm N<PERSON>',
  CONG_NGHE: 'Công Ng<PERSON>ệ'
};

// Các tổ bộ môn chính
const DEPARTMENT_GROUPS = {
  TOAN_TIN: 'Tổ Toán – Tin học',
  TU_NHIEN: 'Tổ Tự nhiên', 
  NGU_VAN: 'Tổ Ngữ văn',
  XA_HOI: 'Tổ Xã hội',
  NGOAI_NGU: 'Tổ Ngoại ngữ',
  MY_THUAT_AM_NHAC: 'Tổ Mỹ thuật - Âm nhạc'
};

// Mapping từ môn học sang tổ bộ môn
const SUBJECT_TO_DEPARTMENT_GROUP = {
  [SUBJECTS.TOAN]: DEPARTMENT_GROUPS.TOAN_TIN,
  [SUBJECTS.TIN_HOC]: DEPARTMENT_GROUPS.TOAN_TIN,
  
  [SUBJECTS.LY]: DEPARTMENT_GROUPS.TU_NHIEN,
  [SUBJECTS.HOA]: DEPARTMENT_GROUPS.TU_NHIEN,
  [SUBJECTS.SINH]: DEPARTMENT_GROUPS.TU_NHIEN,
  
  [SUBJECTS.VAN]: DEPARTMENT_GROUPS.NGU_VAN,
  
  [SUBJECTS.SU]: DEPARTMENT_GROUPS.XA_HOI,
  [SUBJECTS.DIA]: DEPARTMENT_GROUPS.XA_HOI,
  [SUBJECTS.GDCD]: DEPARTMENT_GROUPS.XA_HOI,
  [SUBJECTS.GDTC]: DEPARTMENT_GROUPS.XA_HOI,
  
  [SUBJECTS.ANH]: DEPARTMENT_GROUPS.NGOAI_NGU,
  
  [SUBJECTS.MY_THUAT]: DEPARTMENT_GROUPS.MY_THUAT_AM_NHAC,
  [SUBJECTS.AM_NHAC]: DEPARTMENT_GROUPS.MY_THUAT_AM_NHAC,
  [SUBJECTS.CONG_NGHE]: DEPARTMENT_GROUPS.MY_THUAT_AM_NHAC
};

// Mapping từ tổ bộ môn sang danh sách môn học
const DEPARTMENT_GROUP_TO_SUBJECTS = {
  [DEPARTMENT_GROUPS.TOAN_TIN]: [SUBJECTS.TOAN, SUBJECTS.TIN_HOC],
  [DEPARTMENT_GROUPS.TU_NHIEN]: [SUBJECTS.LY, SUBJECTS.HOA, SUBJECTS.SINH],
  [DEPARTMENT_GROUPS.NGU_VAN]: [SUBJECTS.VAN],
  [DEPARTMENT_GROUPS.XA_HOI]: [SUBJECTS.SU, SUBJECTS.DIA, SUBJECTS.GDCD, SUBJECTS.GDTC],
  [DEPARTMENT_GROUPS.NGOAI_NGU]: [SUBJECTS.ANH],
  [DEPARTMENT_GROUPS.MY_THUAT_AM_NHAC]: [SUBJECTS.MY_THUAT, SUBJECTS.AM_NHAC, SUBJECTS.CONG_NGHE]
};

// Mapping từ subject code (English) sang subject name (Vietnamese) để tương thích với API
const SUBJECT_CODE_TO_NAME = {
  'math': SUBJECTS.TOAN,
  'informatics': SUBJECTS.TIN_HOC,
  'literature': SUBJECTS.VAN,
  'physics': SUBJECTS.LY,
  'chemistry': SUBJECTS.HOA,
  'biology': SUBJECTS.SINH,
  'history': SUBJECTS.SU,
  'geography': SUBJECTS.DIA,
  'civic_education': SUBJECTS.GDCD,
  'physical_education': SUBJECTS.GDTC,
  'english': SUBJECTS.ANH,
  'art': SUBJECTS.MY_THUAT,
  'music': SUBJECTS.AM_NHAC,
  'technology': SUBJECTS.CONG_NGHE
};

// Danh sách tất cả subjects cho validation
const VALID_SUBJECTS = Object.values(SUBJECTS);

// Danh sách tất cả department groups cho validation  
const VALID_DEPARTMENT_GROUPS = Object.values(DEPARTMENT_GROUPS);

// Helper functions
const getDepartmentGroupBySubject = (subject) => {
  return SUBJECT_TO_DEPARTMENT_GROUP[subject] || null;
};

const getSubjectsByDepartmentGroup = (departmentGroup) => {
  return DEPARTMENT_GROUP_TO_SUBJECTS[departmentGroup] || [];
};

const getAllDepartmentGroups = () => {
  return Object.keys(DEPARTMENT_GROUPS).map(key => ({
    code: key,
    name: DEPARTMENT_GROUPS[key],
    subjects: DEPARTMENT_GROUP_TO_SUBJECTS[DEPARTMENT_GROUPS[key]] || []
  }));
};

// Backwards compatibility - keep the old DEPARTMENTS constant for existing code
const DEPARTMENTS = SUBJECTS;

module.exports = {
  // New structure
  SUBJECTS,
  DEPARTMENT_GROUPS,
  SUBJECT_TO_DEPARTMENT_GROUP,
  DEPARTMENT_GROUP_TO_SUBJECTS,
  SUBJECT_CODE_TO_NAME,
  VALID_SUBJECTS,
  VALID_DEPARTMENT_GROUPS,
  
  // Helper functions
  getDepartmentGroupBySubject,
  getSubjectsByDepartmentGroup,
  getAllDepartmentGroups,
  
  // Backwards compatibility
  DEPARTMENTS,
  VALID_DEPARTMENTS: VALID_DEPARTMENT_GROUPS // Fixed: Should be department groups, not subjects
}; 