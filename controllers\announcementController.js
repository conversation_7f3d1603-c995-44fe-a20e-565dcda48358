const asyncHandler = require('express-async-handler');
const Announcement = require('../models/Announcement');
const User = require('../models/User');
const Class = require('../models/Class');
const MESSAGES = require('../constants/messages');
const ROLES = require('../constants/roles');
const { HTTP_STATUS, createSuccessResponse, createErrorResponse } = require('../constants/httpConstants');
const {
  ANNOUNCEMENT_TYPES,
  ANNOUNCEMENT_STATUS,
  DEPARTMENTS,
  ANNOUNCEMENT_TYPE_LABELS,
  ANNOUNCEMENT_STATUS_LABELS
} = require('../constants/announcementConstants');
const { TEACHER_SPECIFIC_ROLES } = require('../constants/userConstants');
const { VALID_DEPARTMENT_GROUPS } = require('../constants/departments');
const { sendImmediateZaloMessage } = require('../jobs/zaloMessageScheduler');

// @desc    Tạo thông báo mới
// @access  Private (yêu cầu x-auth-token)
exports.createAnnouncement = asyncHandler(async (req, res) => {
  const {
    type,
    content,
    recipients,
    zaloConfig,
    status = ANNOUNCEMENT_STATUS.DRAFT
  } = req.body;

  // Kiểm tra các trường bắt buộc
  if (!type || !content) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
    );
  }

  // Kiểm tra req.user có tồn tại
  if (!req.user || !req.user._id) {
    return res.status(HTTP_STATUS.UNAUTHORIZED).json(
      createErrorResponse(MESSAGES.ERROR.UNAUTHORIZED)
    );
  }

  // Kiểm tra quyền tạo thông báo dựa trên type
  const hasPermission = await checkAnnouncementPermission(req.user, type);
  if (!hasPermission) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Validate recipients dựa trên type
  const validationResult = validateRecipients(type, recipients);
  if (!validationResult.isValid) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(validationResult.message)
    );
  }

  const announcement = new Announcement({
    sender: req.user._id,
    type,
    recipients,
    content,
    zaloConfig: zaloConfig || {},
    status,
  });

  await announcement.save();

  // Populate sender information
  await announcement.populate('sender', 'name role specificRole');

  res.status(201).json(announcement);
});

// @desc    Lấy danh sách thông báo (phân trang)
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncements = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    type,
    classId,
    department,
    status
  } = req.query;

  // Xây dựng query dựa trên role và quyền của user
  const query = await buildAnnouncementQuery(req.user, {
    type,
    classId,
    department,
    status
  });

  const announcements = await Announcement.find(query)
    .populate('sender', 'name role specificRole')
    .populate('recipients.class', 'name')
    .populate('recipients.teachers', 'name role')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  const totalAnnouncements = await Announcement.countDocuments(query);
  const totalPages = Math.ceil(totalAnnouncements / limit);

  res.json({
    announcements,
    currentPage: parseInt(page),
    totalPages,
    totalAnnouncements,
  });
});

// @desc    Lấy thông báo mới nhất
// @access  Private (yêu cầu x-auth-token)
exports.getRecentAnnouncements = asyncHandler(async (req, res) => {
  const { limit = 5, classId } = req.query;

  // Xây dựng query dựa trên role và quyền của user
  const query = await buildAnnouncementQuery(req.user, { classId });

  const announcements = await Announcement.find(query)
    .populate('sender', 'name role specificRole')
    .populate('recipients.class', 'name')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit));

  res.json(announcements);
});

// @desc    Lấy chi tiết thông báo
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncementById = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id)
    .populate('sender', 'name role specificRole')
    .populate('recipients.class', 'name')
    .populate('recipients.teachers', 'name role');

  if (!announcement) {
    return res.status(404).json({ msg: MESSAGES.ERROR.NOT_FOUND });
  }

  // Kiểm tra quyền xem thông báo
  const hasPermission = await checkViewPermission(req.user, announcement);
  if (!hasPermission) {
    return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
  }

  res.json(announcement);
});

// @desc    Cập nhật thông báo
// @access  Private (yêu cầu x-auth-token)
exports.updateAnnouncement = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ERROR.NOT_FOUND)
    );
  }

  // Kiểm tra quyền sửa thông báo (chỉ người tạo hoặc admin)
  if (announcement.sender.toString() !== req.user._id.toString() && req.user.role !== ROLES.ADMIN) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Không cho phép sửa thông báo đã gửi
  if (announcement.status === ANNOUNCEMENT_STATUS.SENT) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.CANNOT_EDIT_SENT)
    );
  }

  const { content, recipients, zaloConfig, status } = req.body;

  // Validate recipients nếu có thay đổi
  if (recipients) {
    const validationResult = validateRecipients(announcement.type, recipients);
    if (!validationResult.isValid) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(validationResult.message)
      );
    }
    announcement.recipients = recipients;
  }

  if (content) announcement.content = content;
  if (zaloConfig) announcement.zaloConfig = { ...announcement.zaloConfig, ...zaloConfig };
  if (status) announcement.status = status;
  announcement.updatedAt = Date.now();

  await announcement.save();
  await announcement.populate('sender', 'name role specificRole');

  res.json(announcement);
});

// @desc    Xóa thông báo
// @access  Private (yêu cầu x-auth-token)
exports.deleteAnnouncement = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ERROR.NOT_FOUND)
    );
  }

  // Kiểm tra quyền xóa thông báo (chỉ người tạo hoặc admin)
  if (announcement.sender.toString() !== req.user._id.toString() && req.user.role !== ROLES.ADMIN) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  await Announcement.findByIdAndDelete(req.params.id);

  res.json(createSuccessResponse(null, MESSAGES.ANNOUNCEMENT.DELETED));
});

// @desc    Lấy danh sách loại thông báo và bộ môn
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncementConfig = asyncHandler(async (req, res) => {
  const config = {
    types: ANNOUNCEMENT_TYPES,
    typeLabels: ANNOUNCEMENT_TYPE_LABELS,
    statuses: ANNOUNCEMENT_STATUS,
    statusLabels: ANNOUNCEMENT_STATUS_LABELS,
    departments: VALID_DEPARTMENT_GROUPS
  };

  res.json(config);
});

// @desc    Lấy thống kê thông báo
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncementStats = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;

  let dateFilter = {};
  if (startDate && endDate) {
    dateFilter = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };
  }

  // Thống kê theo type
  const typeStats = await Announcement.aggregate([
    { $match: dateFilter },
    { $group: { _id: '$type', count: { $sum: 1 } } }
  ]);

  // Thống kê theo status
  const statusStats = await Announcement.aggregate([
    { $match: dateFilter },
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);

  // Tổng số thông báo
  const totalAnnouncements = await Announcement.countDocuments(dateFilter);

  res.json({
    total: totalAnnouncements,
    byType: typeStats,
    byStatus: statusStats
  });
});

// @desc    Lấy nhóm Zalo của user cho announcement
// @access  Private (yêu cầu x-auth-token)
exports.getUserZaloGroupsForAnnouncement = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id).select('groupZaloIds');
  
  const zaloGroups = user.groupZaloIds || [];
  
  res.json({
    success: true,
    zaloGroups: zaloGroups.map(group => ({
      groupId: group.groupId,
      groupName: group.groupName,
      addedAt: group.addedAt
    }))
  });
});

// @desc    Gửi thông báo qua Zalo ngay lập tức
// @access  Private (yêu cầu x-auth-token)
exports.sendZaloMessage = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ERROR.NOT_FOUND)
    );
  }

  // Kiểm tra quyền gửi thông báo (chỉ người tạo hoặc admin)
  if (announcement.sender.toString() !== req.user._id.toString() && req.user.role !== ROLES.ADMIN) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Kiểm tra cấu hình Zalo
  if (!announcement.zaloConfig.enabled || !announcement.zaloConfig.groupId) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.ZALO_NOT_CONFIGURED)
    );
  }

  // Kiểm tra đã gửi chưa
  if (announcement.zaloConfig.sent) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.ZALO_ALREADY_SENT)
    );
  }

  try {
    const result = await sendImmediateZaloMessage(announcement._id);
    res.json(createSuccessResponse(result, MESSAGES.ZALO.SEND_SUCCESS));
  } catch (error) {
    console.error('Error sending Zalo message:', error);
    res.status(500).json({
      msg: 'Lỗi khi gửi thông báo qua Zalo',
      error: error.message
    });
  }
});

// ================ HELPER FUNCTIONS ================

// Kiểm tra quyền tạo thông báo
const checkAnnouncementPermission = async (user, type) => {
  switch (type) {
    case ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT:
      return user.role === ROLES.TEACHER || user.role === ROLES.ADMIN;

    case ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER:
      return user.role === ROLES.ADMIN; // Assuming principal is admin

    case ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER:
      return (user.role === ROLES.TEACHER && user.specificRole === TEACHER_SPECIFIC_ROLES.DEPARTMENT_HEAD) ||
             user.role === ROLES.ADMIN;

    case ANNOUNCEMENT_TYPES.ADMIN_TO_ALL:
      return user.role === ROLES.ADMIN;

    default:
      return false;
  }
};

// Validate recipients dựa trên type
const validateRecipients = (type, recipients) => {
  if (!recipients) {
    return { isValid: false, message: 'Recipients không được để trống' };
  }

  switch (type) {
    case ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT:
      if (!recipients.class) {
        return { isValid: false, message: 'Cần chọn lớp học cho thông báo gửi học sinh' };
      }
      break;

    case ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER:
    case ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER:
      if (!recipients.teachers && !recipients.department && !recipients.schoolWide) {
        return { isValid: false, message: 'Cần chọn giáo viên, bộ môn hoặc toàn trường' };
      }
      break;

    case ANNOUNCEMENT_TYPES.ADMIN_TO_ALL:
      if (!recipients.schoolWide) {
        return { isValid: false, message: 'Thông báo admin phải gửi toàn trường' };
      }
      break;

    default:
      return { isValid: false, message: 'Loại thông báo không hợp lệ' };
  }

  return { isValid: true };
};

// Xây dựng query để lấy thông báo dựa trên role và quyền của user
const buildAnnouncementQuery = async (user, filters = {}) => {
  let query = {};

  // Lọc theo role của user
  switch (user.role) {
    case ROLES.STUDENT:
      // Học sinh chỉ xem thông báo gửi cho lớp của mình
      query = {
        type: ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT,
        'recipients.class': user.class,
        status: ANNOUNCEMENT_STATUS.SENT // Chỉ xem thông báo đã gửi
      };
      break;

    case ROLES.TEACHER:
      // Giáo viên xem thông báo gửi cho mình và thông báo mình tạo
      query = {
        $or: [
          // 2. Thông báo từ hiệu trưởng/tổ trưởng cho giáo viên (chỉ đã gửi)
          {
            type: { $in: [ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER, ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER] },
            status: ANNOUNCEMENT_STATUS.SENT,
            $or: [
              { 'recipients.schoolWide': true }, // Gửi toàn trường
              { 'recipients.teachers': user._id }, // Được chỉ định cụ thể
              { 
                'recipients.department': { 
                  $in: [user.department, ...(user.additionalDepartments || [])] 
                } 
              } // Thuộc bộ môn (bao gồm cả bộ môn phụ)
            ]
          },
          
          // 3. Thông báo admin gửi toàn trường (chỉ đã gửi)
          {
            type: ANNOUNCEMENT_TYPES.ADMIN_TO_ALL,
            'recipients.schoolWide': true,
            status: ANNOUNCEMENT_STATUS.SENT
          }
        ]
      };
      break;

    case ROLES.ADMIN:
      // Admin xem tất cả thông báo (tất cả trạng thái)
      break;

    default:
      query = { _id: null }; // Không có quyền xem
  }

  // Áp dụng các filter bổ sung
  if (filters.type) {
    if (user.role === ROLES.ADMIN) {
      query.type = filters.type;
    } else {
      // Với non-admin, cần kết hợp với query hiện tại
      if (Object.keys(query).length > 0) {
        query = { $and: [query, { type: filters.type }] };
      } else {
        query.type = filters.type;
      }
    }
  }

  if (filters.classId && user.role !== ROLES.STUDENT) {
    const classFilter = { 'recipients.class': filters.classId };
    if (Object.keys(query).length > 0 && user.role !== ROLES.ADMIN) {
      query = { $and: [query, classFilter] };
    } else {
      Object.assign(query, classFilter);
    }
  }

  if (filters.department) {
    const deptFilter = { 'recipients.department': filters.department };
    if (Object.keys(query).length > 0 && user.role !== ROLES.ADMIN) {
      query = { $and: [query, deptFilter] };
    } else {
      Object.assign(query, deptFilter);
    }
  }

  if (filters.status) {
    const statusFilter = { status: filters.status };
    if (Object.keys(query).length > 0 && user.role !== ROLES.ADMIN) {
      query = { $and: [query, statusFilter] };
    } else {
      Object.assign(query, statusFilter);
    }
  }

  return query;
};

// Kiểm tra quyền xem chi tiết thông báo
const checkViewPermission = async (user, announcement) => {
  // Admin có thể xem tất cả
  if (user.role === ROLES.ADMIN) {
    return true;
  }

  // Người tạo có thể xem
  if (announcement.sender.toString() === user._id.toString()) {
    return true;
  }

  // Kiểm tra theo type và recipients
  switch (announcement.type) {
    case ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT:
      // Học sinh chỉ xem được thông báo đã gửi cho lớp của mình
      return user.role === ROLES.STUDENT &&
             announcement.status === ANNOUNCEMENT_STATUS.SENT &&
             announcement.recipients.class &&
             announcement.recipients.class.toString() === user.class.toString();

    case ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER:
    case ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER:
      // Giáo viên chỉ xem được thông báo đã gửi và thuộc các điều kiện sau:
      if (user.role !== ROLES.TEACHER || announcement.status !== ANNOUNCEMENT_STATUS.SENT) {
        return false;
      }

      return announcement.recipients.schoolWide || // Gửi toàn trường
             (announcement.recipients.teachers && announcement.recipients.teachers.includes(user._id)) || // Được chỉ định
             (announcement.recipients.department && 
              (user.department === announcement.recipients.department || 
               (user.additionalDepartments && user.additionalDepartments.includes(announcement.recipients.department)))); // Thuộc bộ môn

    case ANNOUNCEMENT_TYPES.ADMIN_TO_ALL:
      // Tất cả user (trừ admin) chỉ xem được thông báo đã gửi toàn trường
      return announcement.status === ANNOUNCEMENT_STATUS.SENT &&
             announcement.recipients.schoolWide;

    default:
      return false;
  }
};