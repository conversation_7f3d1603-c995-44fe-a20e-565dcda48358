# Hướng dẫn tích hợp <PERSON>alo cho hệ thống thông báo

## Tổng quan

Hệ thống đã được tích hợp chức năng gửi thông báo tự động qua Zalo thông qua:
- **Scheduler**: Ch<PERSON><PERSON> mỗi phút để kiểm tra và gửi thông báo đã lên lịch
- **Manual Send**: API để gửi thông báo ngay lập tức
- **Error Tracking**: <PERSON> dõi lỗi khi gửi tin nhắn

## Cách sử dụng

### 1. Body format để tạo thông báo

```javascript
POST /api/announcements
{
  "type": "teacher_to_student", // Required
  "content": "Nội dung thông báo", // Required  
  "recipients": { // Required - format khác nhau theo type
    // Cho teacher_to_student:
    "class": "classId",
    "subject": "<PERSON><PERSON> họ<PERSON>"
    
    // Cho principal_to_teacher hoặc head_to_teacher:
    "teachers": ["teacherId1", "teacherId2"],
    "department": "Toán", 
    "schoolWide": false
    
    // Cho admin_to_all:
    "schoolWide": true
  },
  "zaloConfig": { // Optional
    "enabled": true,
    "groupId": "zaloGroupId", 
    "groupName": "Tên nhóm Zalo",
    "scheduledTime": "2024-01-15T10:00:00Z" // ISO date string
  },
  "status": "draft" // Optional - mặc định "draft"
}
```

### 2. Các loại thông báo và quyền

| Type | Người tạo | Recipient format |
|------|-----------|------------------|
| `teacher_to_student` | Teacher, Admin | `{ class: "classId", subject: "Môn học" }` |
| `principal_to_teacher` | Admin | `{ teachers: [], department: "Bộ môn", schoolWide: boolean }` |
| `head_to_teacher` | Department Head, Admin | `{ teachers: [], department: "Bộ môn", schoolWide: boolean }` |
| `admin_to_all` | Admin | `{ schoolWide: true }` |

### 3. Trạng thái thông báo

- `draft`: Bản nháp
- `scheduled`: Đã lên lịch  
- `sent`: Đã gửi

### 4. API Endpoints

#### Tạo thông báo
```
POST /api/announcements
```

#### Gửi Zalo ngay lập tức  
```
POST /api/announcements/:id/send-zalo
```

#### Lấy nhóm Zalo của user
```
GET /api/announcements/zalo-groups
```

## Cấu hình Zalo

### 1. Environment Variables

Thêm vào file `.env`:
```
ZALO_ACCESS_TOKEN=your_zalo_access_token
```

### 2. Zalo Config trong thông báo

```javascript
"zaloConfig": {
  "enabled": true,           // Bật/tắt gửi Zalo
  "groupId": "123456789",    // ID nhóm Zalo
  "groupName": "Lớp 10A1",   // Tên nhóm (để hiển thị)
  "scheduledTime": "2024-01-15T10:00:00Z", // Thời gian gửi (optional)
  "sent": false,             // Trạng thái đã gửi (auto update)
  "sentAt": null,            // Thời gian đã gửi (auto update)
  "lastError": null,         // Lỗi gần nhất (auto update)
  "lastErrorAt": null        // Thời gian lỗi (auto update)
}
```

## Auto Scheduler

### Cách hoạt động
- Chạy mỗi phút (`* * * * *`)
- Tìm thông báo có:
  - `status`: `scheduled` hoặc `draft`
  - `zaloConfig.enabled`: `true`
  - `zaloConfig.scheduledTime`: <= hiện tại
  - `zaloConfig.sent`: `false`
  - `zaloConfig.groupId`: có giá trị

### Xử lý lỗi
- Ghi log lỗi vào `zaloConfig.lastError` và `lastErrorAt`
- Thông báo không bị đánh dấu là `sent` nếu gửi thất bại
- Console log chi tiết cho debug

### Format tin nhắn Zalo

```
📚 Thông báo từ giáo viên
👤 Từ: Nguyễn Văn A
⏰ Thời gian: 15/01/2024 10:00:00

📝 Nội dung:
Nộp bài tập chương 1 vào thứ 2 tuần sau.

---
🤖 Tin nhắn tự động từ hệ thống quản lý trường học
```

## Workflow thực tế

### Tạo thông báo với Zalo

1. **Lấy danh sách nhóm Zalo của user**
```javascript
GET /api/announcements/zalo-groups
// Response: { zaloGroups: [{ groupId, groupName, addedAt }] }
```

2. **Tạo thông báo với cấu hình Zalo**
```javascript
POST /api/announcements
{
  "type": "teacher_to_student",
  "content": "Thông báo nghỉ học ngày mai",
  "recipients": { "class": "60f7b1b8e4b0a8b5c8d9e0f1" },
  "zaloConfig": {
    "enabled": true,
    "groupId": "123456789", 
    "groupName": "Lớp 10A1",
    "scheduledTime": "2024-01-15T07:00:00Z"
  }
}
```

3. **Gửi ngay (optional)**
```javascript
POST /api/announcements/:id/send-zalo
// Gửi ngay lập tức, bỏ qua scheduledTime
```

### Theo dõi trạng thái

- Kiểm tra field `zaloConfig.sent` để biết đã gửi chưa
- Xem `zaloConfig.lastError` để debug lỗi
- Log server sẽ hiển thị quá trình gửi tin nhắn

## Troubleshooting

### Lỗi thường gặp

1. **"Zalo access token không được cấu hình"**
   - Kiểm tra biến môi trường `ZALO_ACCESS_TOKEN`

2. **"Thông báo chưa được cấu hình Zalo"**
   - Đảm bảo `zaloConfig.enabled = true` và có `groupId`

3. **"Zalo API Error"**
   - Kiểm tra access token còn hạn
   - Đảm bảo groupId tồn tại và bot có quyền gửi tin

### Debug logs

Server sẽ in ra:
```
🕒 Zalo Message Scheduler started - checking every minute
📨 Found 2 announcements to send to Zalo
📤 Sending announcement to Zalo group: Lớp 10A1 (123456789)
✅ Successfully sent announcement 60f7b1b8e4b0a8b5c8d9e0f2 to Zalo group Lớp 10A1
```

## Notes

- Scheduler chạy ngay khi server khởi động
- Hỗ trợ multiple announcements cùng lúc
- Error handling không làm crash server
- Format tin nhắn có thể tùy chỉnh trong `formatAnnouncementMessage()` 