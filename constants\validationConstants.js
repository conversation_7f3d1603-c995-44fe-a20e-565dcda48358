/**
 * Validation Constants - Common validation patterns and messages
 * Centralizes validation logic and patterns used across controllers
 */

// Common validation patterns
const VALIDATION_PATTERNS = {
  PHONE_NUMBER: /^(0|\+84)[0-9]{9,10}$/,
  STUDENT_ID: /^[A-Z0-9]{6,12}$/,
  OBJECT_ID: /^[0-9a-fA-F]{24}$/,
  PASSWORD_MIN_LENGTH: 6,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50
};

// Common validation rules
const VALIDATION_RULES = {
  REQUIRED_FIELDS: {
    USER_REGISTER: ['name', 'phoneNumber', 'password', 'role', 'gender'],
    STUDENT_REGISTER: ['name', 'phoneNumber', 'password', 'studentId', 'class', 'gender'],
    TEACHER_REGISTER: ['name', 'phoneNumber', 'password', 'teachingSubjects', 'gender'],
    LOGIN: ['studentId', 'password'],
    ANNOUNCEMENT: ['type', 'content'],
    GRADE: ['student', 'subject', 'gradeType', 'value', 'semester', 'schoolYear'],
    EXAM: ['title', 'subject', 'description'],
    ATTENDANCE: ['class', 'date', 'attendanceData']
  },
  
  OPTIONAL_FIELDS: {
    USER: ['avatar', 'zaloId'],
    ANNOUNCEMENT: ['zaloConfig', 'status'],
    EXAM: ['timeLimit', 'questions'],
    GRADE: ['description', 'coefficient']
  }
};

// Validation helper functions
const validateRequiredFields = (data, requiredFields) => {
  const missing = requiredFields.filter(field => 
    !data[field] || (typeof data[field] === 'string' && data[field].trim() === '')
  );
  
  return {
    isValid: missing.length === 0,
    missingFields: missing,
    message: missing.length > 0 ? `Thiếu các trường bắt buộc: ${missing.join(', ')}` : null
  };
};

const validateObjectId = (id) => {
  return VALIDATION_PATTERNS.OBJECT_ID.test(id);
};

const validatePhoneNumber = (phone) => {
  return VALIDATION_PATTERNS.PHONE_NUMBER.test(phone);
};

const validateStudentId = (studentId) => {
  return VALIDATION_PATTERNS.STUDENT_ID.test(studentId);
};

const validatePassword = (password) => {
  return password && password.length >= VALIDATION_PATTERNS.PASSWORD_MIN_LENGTH;
};

const validateName = (name) => {
  return name && 
         name.length >= VALIDATION_PATTERNS.NAME_MIN_LENGTH && 
         name.length <= VALIDATION_PATTERNS.NAME_MAX_LENGTH;
};

// Validation error messages
const VALIDATION_MESSAGES = {
  INVALID_PHONE: 'Số điện thoại không hợp lệ',
  INVALID_STUDENT_ID: 'Mã số học sinh không hợp lệ',
  INVALID_OBJECT_ID: 'ID không hợp lệ',
  PASSWORD_TOO_SHORT: `Mật khẩu phải có ít nhất ${VALIDATION_PATTERNS.PASSWORD_MIN_LENGTH} ký tự`,
  INVALID_NAME: `Tên phải có từ ${VALIDATION_PATTERNS.NAME_MIN_LENGTH} đến ${VALIDATION_PATTERNS.NAME_MAX_LENGTH} ký tự`,
  REQUIRED_FIELD: 'Trường này là bắt buộc',
  INVALID_ARRAY: 'Dữ liệu phải là một mảng',
  EMPTY_ARRAY: 'Mảng không được để trống',
  INVALID_ENUM: 'Giá trị không hợp lệ',
  INVALID_DATE: 'Ngày tháng không hợp lệ',
  INVALID_NUMBER: 'Giá trị phải là số',
  INVALID_BOOLEAN: 'Giá trị phải là true hoặc false'
};

module.exports = {
  VALIDATION_PATTERNS,
  VALIDATION_RULES,
  VALIDATION_MESSAGES,
  validateRequiredFields,
  validateObjectId,
  validatePhoneNumber,
  validateStudentId,
  validatePassword,
  validateName
}; 