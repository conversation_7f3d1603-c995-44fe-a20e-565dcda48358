const mongoose = require('mongoose');

const announcementSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', // Người gửi thông báo
    required: true,
  },

  // Lo<PERSON>i thông báo
  type: {
    type: String,
    enum: ['teacher_to_student', 'principal_to_teacher', 'head_to_teacher', 'admin_to_all'],
    required: true,
  },

  // Đối tượng nhận thông báo
  recipients: {
    // Cho thông báo teacher_to_student
    class: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Class',
    },
    subject: {
      type: String, // <PERSON>ôn học cụ thể
    },

    // Cho thông báo principal_to_teacher, head_to_teacher
    teachers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    }],

    // Cho thông báo theo bộ môn
    department: {
      type: String, // Tên bộ môn: "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"...
    },

    // Cho thông báo toàn trường
    schoolWide: {
      type: Boolean,
      default: false,
    }
  },

  content: {
    type: String,
    required: true,
  },

  // Cấu hình thông báo Zalo
  zaloConfig: {
    enabled: {
      type: Boolean,
      default: false,
    },
    groupId: {
      type: String, // ID nhóm Zalo
    },
    groupName: {
      type: String, // Tên nhóm Zalo
    },
    scheduledTime: {
      type: Date, // Thời gian lên lịch gửi
    },
    sent: {
      type: Boolean,
      default: false,
    },
    sentAt: {
      type: Date,
    },
    lastError: {
      type: String, // Lỗi gần nhất khi gửi Zalo
    },
    lastErrorAt: {
      type: Date, // Thời gian lỗi gần nhất
    }
  },

  // Trạng thái thông báo
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'sent'],
    default: 'draft',
  },

  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Index để tối ưu truy vấn
announcementSchema.index({ type: 1, createdAt: -1 });
announcementSchema.index({ 'recipients.class': 1, type: 1 });
announcementSchema.index({ 'recipients.department': 1 });
announcementSchema.index({ 'zaloConfig.scheduledTime': 1, status: 1 });
announcementSchema.index({ 'zaloConfig.enabled': 1, 'zaloConfig.sent': 1 });

module.exports = mongoose.model('Announcement', announcementSchema);