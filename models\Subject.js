// models/Subject.js
const mongoose = require('mongoose');
const { VALID_DEPARTMENTS } = require('../constants/departments');

const SubjectSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  code: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  department: {
    type: String,
    required: true,
    enum: VALID_DEPARTMENTS,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    enum: ['Tự nhiên', 'Xã hội', 'Ngôn ngữ', 'Thể chất', '<PERSON><PERSON><PERSON> thuật', '<PERSON><PERSON> năng'],
    default: 'Tự nhiên'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index cho department để tối ưu query
SubjectSchema.index({ department: 1, isActive: 1 });
SubjectSchema.index({ code: 1 }, { unique: true });

// Pre-save hook để cập nhật updatedAt
SubjectSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Subject', SubjectSchema);