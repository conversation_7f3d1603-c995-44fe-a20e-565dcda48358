// controllers/subjectController.js
const Subject = require('../models/Subject');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');

// @desc    Lấy tất cả các môn học
// @route   GET /api/subjects
// @access  Public
exports.getSubjects = asyncHandler(async (req, res) => {
    // Lấy các tham số lọc và phân trang từ query
    const { department, category, isActive, page = 1, limit = 10, sort = 'name' } = req.query;

    // Xây dựng query
    const query = {};
    if (department) query.department = department;
    if (category) query.category = category;
    if (isActive) query.isActive = isActive === 'true';

    // Đếm tổng số bản ghi thỏa mãn điều kiện
    const total = await Subject.countDocuments(query);

    // L<PERSON>y danh sách môn học
    const subjects = await Subject.find(query)
      .sort(sort)
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit));

    res.json({
      success: true,
      count: subjects.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: subjects
    });
});

// @desc    Lấy thông tin một môn học
// @route   GET /api/subjects/:id
// @access  Public
exports.getSubject = asyncHandler(async (req, res) => {
    try {
      const subject = await Subject.findById(req.params.id);

      if (!subject) {
        return res.status(404).json({ msg: MESSAGES.SUBJECT.NOT_FOUND });
      }

      res.json({
        success: true,
        data: subject
      });
    } catch (err) {
      // Kiểm tra lỗi ID không hợp lệ
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: MESSAGES.SUBJECT.NOT_FOUND });
      }
      throw err; // Chuyển lỗi cho asyncHandler xử lý
    }
});

// @desc    Tạo môn học mới
// @route   POST /api/subjects
// @access  Private (Admin)
exports.createSubject = asyncHandler(async (req, res) => {
    const { name, code, department, description, category, isActive } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name || !code || !department) {
      return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
    }

    // Kiểm tra môn học đã tồn tại chưa (trong cùng department)
    let subject = await Subject.findOne({ 
      $or: [
        { name, department }, 
        { code, department }
      ] 
    });

    if (subject) {
      return res.status(400).json({ msg: MESSAGES.SUBJECT.ALREADY_EXISTS });
    }

    // Tạo môn học mới
    subject = new Subject({
      name,
      code,
      department,
      description,
      category,
      isActive: isActive !== undefined ? isActive : true
    });

    await subject.save();

    res.status(201).json({
      success: true,
      data: subject
    });
});

// @desc    Cập nhật môn học
// @route   PUT /api/subjects/:id
// @access  Private (Admin)
exports.updateSubject = asyncHandler(async (req, res) => {
    try {
      const { name, code, department, description, category, isActive } = req.body;

      // Tìm môn học cần cập nhật
      let subject = await Subject.findById(req.params.id);

      if (!subject) {
        return res.status(404).json({ msg: MESSAGES.SUBJECT.NOT_FOUND });
      }

      // Kiểm tra nếu cập nhật tên, mã hoặc department, xem có trùng với môn học khác không
      if ((name && name !== subject.name) || 
          (code && code !== subject.code) || 
          (department && department !== subject.department)) {
        const existingSubject = await Subject.findOne({
          $and: [
            { _id: { $ne: req.params.id } },
            { 
              department: department || subject.department,
              $or: [
                { name: name || subject.name },
                { code: code || subject.code }
              ]
            }
          ]
        });

        if (existingSubject) {
          return res.status(400).json({ msg: MESSAGES.SUBJECT.ALREADY_EXISTS });
        }
      }

      // Cập nhật thông tin
      subject.name = name || subject.name;
      subject.code = code || subject.code;
      subject.department = department || subject.department;
      subject.description = description !== undefined ? description : subject.description;
      subject.category = category || subject.category;
      subject.isActive = isActive !== undefined ? isActive : subject.isActive;
      subject.updatedAt = Date.now();

      await subject.save();

      res.json({
        success: true,
        data: subject
      });
    } catch (err) {
      // Kiểm tra lỗi ID không hợp lệ
      if (err.kind === 'ObjectId') {
        return res.status(404).json({ msg: MESSAGES.SUBJECT.NOT_FOUND });
      }
      throw err; // Chuyển lỗi cho asyncHandler xử lý
    }
});

// @desc    Xóa môn học
// @route   DELETE /api/subjects/:id
// @access  Private (Admin)
exports.deleteSubject = asyncHandler(async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id);

    if (!subject) {
      return res.status(404).json({ msg: MESSAGES.SUBJECT.NOT_FOUND });
    }

    await subject.deleteOne();

    res.json({
      success: true,
      msg: MESSAGES.SUBJECT.DELETED
    });
  } catch (err) {
    // Kiểm tra lỗi ID không hợp lệ
    if (err.kind === 'ObjectId') {
      return res.status(404).json({ msg: MESSAGES.SUBJECT.NOT_FOUND });
    }
    throw err; // Chuyển lỗi cho asyncHandler xử lý
  }
});