/**
 * HTTP Constants - Status codes and response patterns
 * Centralizes HTTP status codes and common response structures
 */

// HTTP Status Codes
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500
};

// Standard Response Structure
const RESPONSE_STRUCTURE = {
  SUCCESS: {
    success: true
  },
  ERROR: {
    success: false
  }
};

// Common Response Patterns
const createSuccessResponse = (data = null, message = null) => ({
  success: true,
  ...(message && { message }),
  ...(data && { data })
});

const createErrorResponse = (message, error = null) => ({
  success: false,
  msg: message,
  ...(error && { error })
});

const createPaginatedResponse = (data, count, page = 1, limit = 10) => ({
  success: true,
  count,
  page: parseInt(page),
  limit: parseInt(limit),
  totalPages: Math.ceil(count / limit),
  data
});

module.exports = {
  HTTP_STATUS,
  RESPONSE_STRUCTURE,
  createSuccessResponse,
  createErrorResponse,
  createPaginatedResponse
}; 